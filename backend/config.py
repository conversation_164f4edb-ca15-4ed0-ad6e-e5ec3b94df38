"""
ComplianceGPT Configuration

Central configuration management for the ComplianceGPT platform.
"""

import os
from typing import Dict, Any
from dotenv import load_dotenv

# Determine the directory of the current file (config.py)
current_dir = os.path.dirname(os.path.abspath(__file__))
# Construct the path to the .env file, assuming it's in the same directory as config.py
dotenv_path = os.path.join(current_dir, '.env')

# Load environment variables from .env file
if os.path.exists(dotenv_path):
    load_dotenv(dotenv_path=dotenv_path)
    print(f"DEBUG: Loaded .env file from: {dotenv_path}") # Added for verification
else:
    print(f"DEBUG: .env file not found at: {dotenv_path}. Relying on environment variables.") # Added for verification

# API Configuration
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
JWT_SECRET = os.getenv("JWT_SECRET")
JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
JWT_EXPIRATION_HOURS = int(os.getenv("JWT_EXPIRATION_HOURS", "24"))
LANGTRACE_API_KEY = os.getenv("LANGTRACE_API_KEY") # For Langtrace observability

if not JWT_SECRET:
    raise EnvironmentError("CRITICAL: JWT_SECRET environment variable not set. Application cannot start securely.")
if JWT_SECRET == "your-secret-key-change-this-in-production":
    # Log a warning and raise an error to prevent running with the default insecure key
    # In a real production scenario, this might be handled by deployment scripts or startup checks
    # that prevent deployment/startup if critical secrets are missing or set to defaults.
    print("CRITICAL ERROR: JWT_SECRET is set to the default insecure value. Please set a strong, unique secret in your environment variables.")
    raise ValueError("CRITICAL: Insecure JWT_SECRET configured. Application aborted.")
if not GEMINI_API_KEY:
    raise EnvironmentError("CRITICAL: GEMINI_API_KEY environment variable not set. Application cannot start securely.")

# Database Configuration  
MONGO_URL = os.environ.get('MONGO_URL', 'mongodb://localhost:27017/')
DB_NAME = os.environ.get('DB_NAME', 'compliancegpt')

# Gemini AI Configuration
GEMINI_CONFIG = {
    "temperature": 0.3,  # Lower temperature for consistent compliance content
    "top_p": 0.8,
    "top_k": 40,
    "max_output_tokens": 8000,
}

# Supported Models
MODELS = {
    "basic": "gemini-2.0-flash-exp",
    "thinking": "gemini-2.0-flash-thinking-exp-1219"
}

# Compliance Frameworks Configuration
FRAMEWORKS = {
    "gdpr": {
        "id": "gdpr",
        "name": "GDPR (General Data Protection Regulation)",
        "description": "EU data protection and privacy regulation for individuals within the EU and EEA",
        "controls": [
            {"id": "gdpr_1", "name": "Lawful Basis for Processing", "requirement": "Article 6 - Establish lawful basis for data processing"},
            {"id": "gdpr_2", "name": "Data Subject Rights", "requirement": "Articles 15-22 - Implement data subject rights procedures"},
            {"id": "gdpr_3", "name": "Privacy by Design", "requirement": "Article 25 - Implement privacy by design and default"},
            {"id": "gdpr_4", "name": "Data Protection Impact Assessment", "requirement": "Article 35 - Conduct DPIA for high-risk processing"},
            {"id": "gdpr_5", "name": "Data Breach Notification", "requirement": "Articles 33-34 - Implement breach notification procedures"},
            {"id": "gdpr_6", "name": "Data Processing Records", "requirement": "Article 30 - Maintain records of processing activities"},
            {"id": "gdpr_7", "name": "Data Retention Policy", "requirement": "Article 5 - Implement data minimization and retention limits"}
        ],
        "requirements": [
            "Lawful basis for data processing",
            "Data subject rights implementation", 
            "Privacy impact assessments",
            "Data breach notification procedures",
            "Data processing records",
            "Data retention policies"
        ]
    },
    "soc2": {
        "id": "soc2",
        "name": "SOC 2 Type II",
        "description": "Security, availability, processing integrity, confidentiality, and privacy controls",
        "controls": [
            {"id": "soc2_1", "name": "Security Policies", "requirement": "CC1.1 - Establish security policies and procedures"},
            {"id": "soc2_2", "name": "Access Controls", "requirement": "CC6.1-6.8 - Implement logical and physical access controls"},
            {"id": "soc2_3", "name": "System Monitoring", "requirement": "CC7.1-7.5 - Monitor system activities and security events"},
            {"id": "soc2_4", "name": "Change Management", "requirement": "CC8.1 - Implement change management procedures"},
            {"id": "soc2_5", "name": "Incident Response", "requirement": "A1.1-1.3 - Establish incident response procedures"},
            {"id": "soc2_6", "name": "Business Continuity", "requirement": "A1.2 - Implement business continuity planning"},
            {"id": "soc2_7", "name": "Vendor Management", "requirement": "CC9.1-9.2 - Manage third-party relationships"}
        ],
        "requirements": [
            "Information security policies",
            "Access control procedures",
            "System monitoring and logging",
            "Change management processes",
            "Incident response planning",
            "Business continuity procedures"
        ]
    },
    "iso27001": {
        "id": "iso27001", 
        "name": "ISO 27001:2022",
        "description": "International standard for information security management systems",
        "controls": [
            {"id": "iso_1", "name": "Information Security Policy", "requirement": "A.5.1 - Establish information security policy"},
            {"id": "iso_2", "name": "Risk Management", "requirement": "A.5.2 - Implement information security risk management"},
            {"id": "iso_3", "name": "Asset Management", "requirement": "A.8 - Implement asset management controls"},
            {"id": "iso_4", "name": "Access Control", "requirement": "A.9 - Implement access control management"},
            {"id": "iso_5", "name": "Cryptography", "requirement": "A.10 - Implement cryptographic controls"},
            {"id": "iso_6", "name": "Operations Security", "requirement": "A.12 - Implement operations security controls"},
            {"id": "iso_7", "name": "Incident Management", "requirement": "A.16 - Implement incident management procedures"}
        ],
        "requirements": [
            "Information security management system",
            "Risk assessment and treatment",
            "Asset inventory and classification",
            "Access control procedures",
            "Cryptographic key management",
            "Security incident management"
        ]
    }
}

# Application Configuration
APP_CONFIG = {
    "title": "ComplianceGPT API",
    "version": "1.0.0",
    "description": "AI-powered compliance automation platform for UK SMBs"
}